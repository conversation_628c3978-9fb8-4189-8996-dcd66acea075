<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Error Analysis Dashboard</title>
    <style>
        body { font-family: sans-serif; }
        .dashboard-container {
            width: 95%;
            margin: 20px auto;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 20px; /* 控制图表间的间距 */
        }
        .chart-container {
            width: calc(50% - 10px); /* 让每行可以放两个图表，并考虑间距 */
            height: 400px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 10px;
            box-sizing: border-box;
            background: #fff;
        }
        h1 { text-align: center; }
        .controls {
            text-align: center;
            margin-bottom: 20px;
        }
        .controls label, .controls input, .controls button {
            font-size: 16px;
            margin: 0 5px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.3.2/dist/echarts.min.js"></script>
</head>
<body>
    <h1>Error Analysis Dashboard</h1>

    <div class="controls">
        <label for="startDate">Start Date:</label>
        <input type="date" id="startDate">
        <label for="endDate">End Date:</label>
        <input type="date" id="endDate">
        <button onclick="updateCharts()">Query</button>
    </div>

    <div class="dashboard-container">
        <div id="errorTypesChart" class="chart-container"></div>
        <div id="errorLocationsChart" class="chart-container"></div>
        <div id="timeTrendsChart" class="chart-container"></div>
    </div>

    <script type="text/javascript">
        // Initialize ECharts instances
        var errorTypesChart = echarts.init(document.getElementById('errorTypesChart'));
        var errorLocationsChart = echarts.init(document.getElementById('errorLocationsChart'));
        var timeTrendsChart = echarts.init(document.getElementById('timeTrendsChart'));

        // Function to fetch data and render a pie chart
        function renderPieChart(chart, url, title) {
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                         chart.setOption({ title: { text: data.error, left: 'center' } });
                         return;
                    }
                    var option = {
                        title: { text: title, left: 'center' },
                        tooltip: { trigger: 'item' },
                        legend: { orient: 'vertical', left: 'left' },
                        series: [
                            {
                                name: title,
                                type: 'pie',
                                radius: '50%',
                                data: data.seriesData
                            }
                        ]
                    };
                    chart.setOption(option);
                })
                .catch(error => console.error(`Error fetching ${title} data:`, error));
        }
        
        // Function to fetch data and render a bar chart
        function renderBarChart(chart, url, title) {
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                         chart.setOption({ title: { text: data.error, left: 'center' } });
                         return;
                    }
                    var option = {
                        title: { text: title, left: 'center' },
                        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
                        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                        xAxis: { type: 'value' },
                        yAxis: { type: 'category', data: data.xAxisData },
                        series: [
                            {
                                name: title,
                                type: 'bar',
                                data: data.seriesData
                            }
                        ]
                    };
                    chart.setOption(option);
                })
                .catch(error => console.error(`Error fetching ${title} data:`, error));
        }

        // Function to fetch data and render a line chart
        function renderLineChart(chart, url, title) {
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                         chart.setOption({ title: { text: data.error, left: 'center' } });
                         return;
                    }
                    var option = {
                        title: { text: title, left: 'center' },
                        tooltip: { trigger: 'axis' },
                        xAxis: { type: 'category', data: data.xAxisData },
                        yAxis: { type: 'value' },
                        series: [{
                            name: title,
                            type: 'line',
                            data: data.seriesData
                        }]
                    };
                    chart.setOption(option);
                })
                .catch(error => console.error(`Error fetching ${title} data:`, error));
        }

        // Main function to update all charts
        function updateCharts() {
            var startDate = document.getElementById('startDate').value;
            var endDate = document.getElementById('endDate').value;
            var queryString = `?start_date=${startDate}&end_date=${endDate}`;
            
            // Render each chart with the query string
            renderPieChart(errorTypesChart, `/api/error_types${queryString}`, 'Top 5 Error Types');
            renderBarChart(errorLocationsChart, `/api/error_locations${queryString}`, 'Top 5 Error Locations');
            renderLineChart(timeTrendsChart, `/api/time_trends${queryString}`, 'Daily Total Error Trend');
        }

        // Initial rendering on page load
        updateCharts();
    </script>
</body>
</html>