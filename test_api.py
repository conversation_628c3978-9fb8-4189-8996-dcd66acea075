#!/usr/bin/env python3
"""
API接口测试脚本
用于测试所有API接口的功能和性能
"""

import requests
import time
import json
import sys
from datetime import datetime

# API基础地址
BASE_URL = "http://localhost:5000"

def test_endpoint(method, endpoint, data=None, expected_status=200):
    """测试单个API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        start_time = time.time()
        
        if method.upper() == "GET":
            response = requests.get(url, timeout=30)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=30)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, timeout=30)
        else:
            print(f"❌ 不支持的HTTP方法: {method}")
            return False
        
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        if response.status_code == expected_status:
            print(f"✅ {method} {endpoint} - {response.status_code} ({response_time:.2f}ms)")
            return True, response.json() if response.content else None
        else:
            print(f"❌ {method} {endpoint} - 期望 {expected_status}, 实际 {response.status_code}")
            print(f"   响应: {response.text[:200]}...")
            return False, None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ {method} {endpoint} - 请求失败: {str(e)}")
        return False, None

def test_basic_endpoints():
    """测试基础端点"""
    print("🔍 测试基础端点...")
    
    # 测试根端点
    success, _ = test_endpoint("GET", "/")
    if not success:
        print("❌ 服务器可能未启动或不可访问")
        return False
    
    # 测试API文档端点
    test_endpoint("GET", "/docs", expected_status=200)
    test_endpoint("GET", "/openapi.json", expected_status=200)
    
    return True

def test_filter_options():
    """测试筛选选项接口"""
    print("\n🔍 测试筛选选项接口...")
    
    success, data = test_endpoint("GET", "/api/filter/options")
    if success and data:
        print(f"   环境数量: {len(data.get('envs', []))}")
        print(f"   版本数量: {len(data.get('versions', []))}")
        print(f"   等级数量: {len(data.get('levels', []))}")
        
        if data.get('envs'):
            print(f"   环境示例: {data['envs'][:3]}")
        if data.get('versions'):
            print(f"   版本示例: {data['versions'][:3]}")
        if data.get('levels'):
            print(f"   等级示例: {data['levels'][:3]}")
    
    return success

def test_error_stats():
    """测试错误统计接口"""
    print("\n🔍 测试错误统计接口...")
    
    # 测试默认参数
    success, data = test_endpoint("GET", "/api/error/stats")
    if success and data:
        print(f"   总错误数: {data.get('total_errors', 0)}")
        print(f"   独特错误类型: {data.get('unique_errors', 0)}")
        print(f"   处理率: {data.get('resolution_rate', 0)}%")
        print(f"   错误率: {data.get('error_rate', 0)}%")
    
    # 测试不同时间范围
    time_ranges = ["7d", "30d", "90d", "all"]
    for time_range in time_ranges:
        test_endpoint("GET", f"/api/error/stats?time_range={time_range}")
    
    return success

def test_error_list():
    """测试错误列表接口"""
    print("\n🔍 测试错误列表接口...")
    
    # 测试默认分页
    success, data = test_endpoint("GET", "/api/errors")
    if success and data:
        print(f"   总记录数: {data.get('total', 0)}")
        print(f"   当前页: {data.get('page', 0)}")
        print(f"   每页数量: {data.get('per_page', 0)}")
        print(f"   总页数: {data.get('pages', 0)}")
        print(f"   返回记录数: {len(data.get('errors', []))}")
    
    # 测试分页
    test_endpoint("GET", "/api/errors?page=1&per_page=10")
    test_endpoint("GET", "/api/errors?page=2&per_page=5")
    
    # 测试筛选
    test_endpoint("GET", "/api/errors?time_range=7d")
    test_endpoint("GET", "/api/errors?level=10")
    test_endpoint("GET", "/api/errors?search=error")
    
    return success, data

def test_error_detail():
    """测试错误详情接口"""
    print("\n🔍 测试错误详情接口...")
    
    # 首先获取一个错误ID
    success, data = test_endpoint("GET", "/api/errors?per_page=1")
    if not success or not data or not data.get('errors'):
        print("⚠️  没有错误记录可供测试")
        return False
    
    error_id = data['errors'][0]['_id']
    print(f"   使用错误ID: {error_id}")
    
    # 测试获取错误详情
    success, detail = test_endpoint("GET", f"/api/errors/{error_id}")
    if success and detail:
        print(f"   错误时间: {detail.get('time', '未知')}")
        print(f"   错误环境: {detail.get('env_name', '未知')}")
        print(f"   错误等级: {detail.get('level', '未知')}")
        print(f"   处理状态: {'已处理' if detail.get('tackle') else '未处理'}")
    
    # 测试更新处理状态
    current_tackle = detail.get('tackle', False) if detail else False
    new_tackle = not current_tackle
    
    success, update_result = test_endpoint(
        "PUT", 
        f"/api/errors/{error_id}/tackle",
        data={"tackle": new_tackle}
    )
    
    if success and update_result:
        print(f"   状态更新成功: {update_result.get('message', '')}")
        
        # 恢复原状态
        test_endpoint(
            "PUT", 
            f"/api/errors/{error_id}/tackle",
            data={"tackle": current_tackle}
        )
    
    # 测试无效ID
    test_endpoint("GET", "/api/errors/invalid_id", expected_status=400)
    
    return success

def test_error_distributions():
    """测试错误分布接口"""
    print("\n🔍 测试错误分布接口...")
    
    success, data = test_endpoint("GET", "/api/error/distributions")
    if success and data:
        print(f"   用户分布数量: {len(data.get('by_user', []))}")
        print(f"   环境分布数量: {len(data.get('by_env', []))}")
        print(f"   等级分布数量: {len(data.get('by_level', []))}")
        print(f"   类型分布数量: {len(data.get('by_type', []))}")
        print(f"   版本分布数量: {len(data.get('by_version', []))}")
        
        # 显示一些示例数据
        if data.get('by_env'):
            print(f"   环境分布示例: {data['by_env'][0]}")
        if data.get('by_level'):
            print(f"   等级分布示例: {data['by_level'][0]}")
    
    # 测试不同参数
    test_endpoint("GET", "/api/error/distributions?time_range=30d")
    test_endpoint("GET", "/api/error/distributions?env=Y4")
    
    return success

def performance_test():
    """性能测试"""
    print("\n⚡ 性能测试...")
    
    endpoints = [
        "/api/filter/options",
        "/api/error/stats",
        "/api/errors?per_page=20",
        "/api/error/distributions"
    ]
    
    for endpoint in endpoints:
        times = []
        for i in range(5):  # 测试5次
            start_time = time.time()
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=30)
            end_time = time.time()
            
            if response.status_code == 200:
                times.append((end_time - start_time) * 1000)
        
        if times:
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            print(f"   {endpoint}: 平均 {avg_time:.2f}ms, 最小 {min_time:.2f}ms, 最大 {max_time:.2f}ms")

def main():
    """主测试函数"""
    print("🧪 Monster框架错误分析API测试")
    print("=" * 50)
    print(f"📍 测试地址: {BASE_URL}")
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查服务是否可用
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code != 200:
            print(f"❌ 服务不可用，状态码: {response.status_code}")
            sys.exit(1)
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务: {str(e)}")
        print("请确保API服务正在运行在 http://localhost:5000")
        sys.exit(1)
    
    print("✅ 服务连接正常")
    
    # 运行测试
    tests = [
        ("基础端点", test_basic_endpoints),
        ("筛选选项", test_filter_options),
        ("错误统计", test_error_stats),
        ("错误列表", test_error_list),
        ("错误详情", test_error_detail),
        ("错误分布", test_error_distributions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
    
    # 性能测试
    try:
        performance_test()
    except Exception as e:
        print(f"⚠️  性能测试失败: {str(e)}")
    
    # 测试结果
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过!")
        sys.exit(0)
    else:
        print("⚠️  部分测试失败，请检查API服务")
        sys.exit(1)

if __name__ == "__main__":
    main()
