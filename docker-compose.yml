version: '3.8'

services:
  api:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./db_config.json:/app/db_config.json:ro
    environment:
      - PYTHONPATH=/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 如果需要本地MongoDB实例，可以取消注释以下配置
  # mongodb:
  #   image: mongo:6.0
  #   ports:
  #     - "27017:27017"
  #   environment:
  #     MONGO_INITDB_ROOT_USERNAME: admin
  #     MONGO_INITDB_ROOT_PASSWORD: password
  #   volumes:
  #     - mongodb_data:/data/db
  #   restart: unless-stopped

# volumes:
#   mongodb_data:
