#!/usr/bin/env python3
"""
部署脚本 - 用于生产环境部署
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e.stderr}")
        sys.exit(1)

def check_requirements():
    """检查部署要求"""
    print("🔍 检查部署要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 检查必需文件
    required_files = ['app.py', 'requirements.txt', 'db_config.json']
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少必需文件: {file}")
            sys.exit(1)
    
    print("✅ 部署要求检查通过")

def install_production_dependencies():
    """安装生产环境依赖"""
    print("📦 安装生产环境依赖...")
    
    # 升级pip
    run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip")
    
    # 安装基础依赖
    run_command(f"{sys.executable} -m pip install -r requirements.txt", "安装基础依赖")
    
    # 安装生产环境额外依赖
    production_deps = [
        "gunicorn==21.2.0",  # WSGI服务器
        "python-json-logger==2.0.7",  # 结构化日志
    ]
    
    for dep in production_deps:
        run_command(f"{sys.executable} -m pip install {dep}", f"安装 {dep}")

def create_systemd_service():
    """创建systemd服务文件"""
    service_content = f"""[Unit]
Description=Monster Framework Error Analysis API
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory={os.getcwd()}
Environment=PATH={os.getcwd()}/venv/bin
ExecStart={sys.executable} -m gunicorn app:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:5000
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
"""
    
    service_file = "/etc/systemd/system/monster-api.service"
    
    try:
        with open(service_file, 'w') as f:
            f.write(service_content)
        print(f"✅ 创建systemd服务文件: {service_file}")
        
        # 重新加载systemd
        run_command("sudo systemctl daemon-reload", "重新加载systemd")
        run_command("sudo systemctl enable monster-api", "启用服务")
        
    except PermissionError:
        print("⚠️  需要sudo权限创建systemd服务文件")
        print(f"请手动创建 {service_file} 文件，内容如下：")
        print(service_content)

def create_nginx_config():
    """创建Nginx配置"""
    nginx_config = """server {
    listen 80;
    server_name your-domain.com;  # 请修改为实际域名
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 8k;
        proxy_buffers 8 8k;
    }
    
    # 静态文件缓存
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 健康检查
    location /health {
        access_log off;
        proxy_pass http://127.0.0.1:5000/;
    }
}
"""
    
    config_file = "/etc/nginx/sites-available/monster-api"
    
    try:
        with open(config_file, 'w') as f:
            f.write(nginx_config)
        
        # 创建软链接
        link_file = "/etc/nginx/sites-enabled/monster-api"
        if not os.path.exists(link_file):
            os.symlink(config_file, link_file)
        
        print(f"✅ 创建Nginx配置文件: {config_file}")
        print("⚠️  请修改配置文件中的域名，然后重启Nginx")
        
    except PermissionError:
        print("⚠️  需要sudo权限创建Nginx配置文件")
        print(f"请手动创建 {config_file} 文件，内容如下：")
        print(nginx_config)

def deploy_docker():
    """Docker部署"""
    print("🐳 Docker部署...")
    
    # 检查Docker
    try:
        subprocess.run(["docker", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Docker未安装或不可用")
        sys.exit(1)
    
    # 构建镜像
    run_command("docker build -t monster-api .", "构建Docker镜像")
    
    # 停止现有容器
    subprocess.run(["docker", "stop", "monster-api"], capture_output=True)
    subprocess.run(["docker", "rm", "monster-api"], capture_output=True)
    
    # 启动新容器
    run_command(
        "docker run -d --name monster-api -p 5000:5000 -v $(pwd)/db_config.json:/app/db_config.json:ro --restart unless-stopped monster-api",
        "启动Docker容器"
    )
    
    print("✅ Docker部署完成")
    print("🌐 服务地址: http://localhost:5000")

def deploy_systemd():
    """Systemd部署"""
    print("⚙️  Systemd部署...")
    
    # 安装依赖
    install_production_dependencies()
    
    # 创建服务文件
    create_systemd_service()
    
    # 创建Nginx配置
    create_nginx_config()
    
    print("✅ Systemd部署配置完成")
    print("🔧 请执行以下命令启动服务:")
    print("   sudo systemctl start monster-api")
    print("   sudo systemctl status monster-api")

def main():
    parser = argparse.ArgumentParser(description="Monster API部署脚本")
    parser.add_argument(
        "--method",
        choices=["docker", "systemd"],
        default="docker",
        help="部署方法 (默认: docker)"
    )
    
    args = parser.parse_args()
    
    print("🚀 Monster框架错误分析API部署脚本")
    print("=" * 50)
    
    # 检查要求
    check_requirements()
    
    # 根据选择的方法部署
    if args.method == "docker":
        deploy_docker()
    elif args.method == "systemd":
        deploy_systemd()
    
    print("\n🎉 部署完成!")
    print("📖 API文档: http://your-server:5000/docs")

if __name__ == "__main__":
    main()
