from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pymongo import MongoClient
from bson import ObjectId
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import json
import re
from pydantic import BaseModel

# 加载数据库配置
with open("db_config.json", "r", encoding="utf-8") as f:
    db_config = json.load(f)

# 创建FastAPI应用
app = FastAPI(
    title="Monster框架错误分析API",
    description="Monster框架错误分析看板后端API",
    version="1.0.0",
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# MongoDB连接
def get_database():
    try:
        client = MongoClient(
            host=db_config["host"],
            port=db_config["port"],
            username=db_config["username"],
            password=db_config["password"],
            authSource=db_config["authSource"],
        )
        db = client[db_config["db_name"]]
        return db
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库连接失败: {str(e)}")


# 数据模型
class TackleUpdateRequest(BaseModel):
    tackle: bool


# 辅助函数
def extract_error_type(message: str) -> str:
    """从错误消息中提取错误类型"""
    match = re.search(r"<([^>]+)>", message)
    return match.group(1) if match else "未知错误"


def extract_version(version_string: str) -> str:
    """提取版本号"""
    parts = version_string.split("|")
    return parts[1].strip() if len(parts) > 1 else version_string


def build_time_filter(time_range: str) -> Dict:
    """构建时间过滤条件"""
    if time_range == "all":
        return {}

    now = datetime.now()
    if time_range == "7d":
        start_date = now - timedelta(days=7)
    elif time_range == "30d":
        start_date = now - timedelta(days=30)
    elif time_range == "90d":
        start_date = now - timedelta(days=90)
    else:
        return {}

    return {"create_time": {"$gte": start_date}}


def build_filters(
    time_range: Optional[str] = None,
    env: Optional[str] = None,
    version: Optional[str] = None,
    level: Optional[str] = None,
    search: Optional[str] = None,
) -> Dict:
    """构建查询过滤条件"""
    filters = {}

    # 时间过滤
    if time_range and time_range != "all":
        filters.update(build_time_filter(time_range))

    # 环境过滤
    if env and env != "all":
        filters["env_name"] = env

    # 版本过滤
    if version and version != "all":
        filters["version"] = {"$regex": version, "$options": "i"}

    # 等级过滤
    if level and level != "all":
        filters["level"] = int(level)

    # 搜索过滤
    if search:
        filters["$or"] = [
            {"message": {"$regex": search, "$options": "i"}},
            {"sample": {"$regex": search, "$options": "i"}},
            {"env_name": {"$regex": search, "$options": "i"}},
        ]

    return filters


# API路由


@app.get("/")
async def root():
    return {"message": "Monster框架错误分析API服务正在运行"}


@app.get("/api/filter/options")
async def get_filter_options():
    """获取筛选选项"""
    try:
        db = get_database()
        collection = db[db_config["collection_name"]]

        # 获取所有环境
        envs = collection.distinct("env_name")

        # 获取所有版本
        versions_raw = collection.distinct("version")
        versions = [extract_version(v) for v in versions_raw]
        versions = list(set(versions))  # 去重

        # 获取所有等级
        levels = collection.distinct("level")
        levels.sort()

        return {"envs": sorted(envs), "versions": sorted(versions), "levels": levels}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取筛选选项失败: {str(e)}")


@app.get("/api/error/stats")
async def get_error_stats(
    time_range: Optional[str] = Query("all"),
    env: Optional[str] = Query("all"),
    version: Optional[str] = Query("all"),
    level: Optional[str] = Query("all"),
):
    """获取错误统计数据"""
    try:
        db = get_database()
        collection = db[db_config["collection_name"]]

        filters = build_filters(time_range, env, version, level)

        # 总错误数量
        total_errors = collection.count_documents(filters)

        # 独特错误类型数量
        pipeline = [
            {"$match": filters},
            {"$group": {"_id": "$message", "count": {"$sum": 1}}},
        ]
        unique_errors = len(list(collection.aggregate(pipeline)))

        # 处理率
        handled_count = collection.count_documents({**filters, "tackle": True})
        resolution_rate = round(
            (handled_count / total_errors * 100) if total_errors > 0 else 0, 1
        )

        # 错误率（这里简化为总错误数相对于某个基准的比例）
        # 实际应用中可能需要根据具体业务逻辑计算
        error_rate = min(100, round(total_errors / 100, 1))  # 简化计算

        return {
            "total_errors": total_errors,
            "unique_errors": unique_errors,
            "resolution_rate": resolution_rate,
            "error_rate": error_rate,
            "total_errors_change": "0%",  # 需要历史数据对比
            "unique_errors_change": "0%",
            "resolution_rate_change": "0%",
            "error_rate_change": "0%",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取错误统计失败: {str(e)}")


@app.get("/api/errors")
async def get_errors(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    time_range: Optional[str] = Query("all"),
    env: Optional[str] = Query("all"),
    version: Optional[str] = Query("all"),
    level: Optional[str] = Query("all"),
    search: Optional[str] = Query(None),
):
    """获取错误列表"""
    try:
        db = get_database()
        collection = db[db_config["collection_name"]]

        filters = build_filters(time_range, env, version, level, search)

        # 计算总数
        total = collection.count_documents(filters)

        # 分页查询
        skip = (page - 1) * per_page
        cursor = (
            collection.find(filters).sort("create_time", -1).skip(skip).limit(per_page)
        )

        errors = []
        for doc in cursor:
            errors.append(
                {
                    "_id": str(doc["_id"]),
                    "username": doc.get("username", ""),
                    "sample": doc.get("sample", ""),
                    "env_name": doc.get("env_name", ""),
                    "level": doc.get("level", 0),
                    "time": doc.get("time", ""),
                    "message": doc.get("message", ""),
                    "version": doc.get("version", ""),
                    "tackle": doc.get("tackle", False),
                    "create_time": doc.get("create_time", ""),
                }
            )

        return {
            "errors": errors,
            "total": total,
            "page": page,
            "per_page": per_page,
            "pages": (total + per_page - 1) // per_page,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取错误列表失败: {str(e)}")


@app.get("/api/errors/{error_id}")
async def get_error_detail(error_id: str):
    """获取错误详情"""
    try:
        db = get_database()
        collection = db[db_config["collection_name"]]

        doc = collection.find_one({"_id": ObjectId(error_id)})
        if not doc:
            raise HTTPException(status_code=404, detail="错误记录不存在")

        return {
            "_id": str(doc["_id"]),
            "username": doc.get("username", ""),
            "sample": doc.get("sample", ""),
            "env_name": doc.get("env_name", ""),
            "level": doc.get("level", 0),
            "time": doc.get("time", ""),
            "message": doc.get("message", ""),
            "version": doc.get("version", ""),
            "tackle": doc.get("tackle", False),
            "create_time": doc.get("create_time", ""),
        }
    except Exception as e:
        if "not a valid ObjectId" in str(e):
            raise HTTPException(status_code=400, detail="无效的错误ID")
        raise HTTPException(status_code=500, detail=f"获取错误详情失败: {str(e)}")


@app.put("/api/errors/{error_id}/tackle")
async def update_error_tackle_status(error_id: str, request: TackleUpdateRequest):
    """更新错误处理状态"""
    try:
        db = get_database()
        collection = db[db_config["collection_name"]]

        result = collection.update_one(
            {"_id": ObjectId(error_id)}, {"$set": {"tackle": request.tackle}}
        )

        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="错误记录不存在")

        return {
            "success": True,
            "tackle": request.tackle,
            "message": f"错误已{'标记为已处理' if request.tackle else '标记为未处理'}",
        }
    except Exception as e:
        if "not a valid ObjectId" in str(e):
            raise HTTPException(status_code=400, detail="无效的错误ID")
        raise HTTPException(status_code=500, detail=f"更新错误状态失败: {str(e)}")


@app.get("/api/error/distributions")
async def get_error_distributions(
    time_range: Optional[str] = Query("all"),
    env: Optional[str] = Query("all"),
    version: Optional[str] = Query("all"),
    level: Optional[str] = Query("all"),
):
    """获取错误分布数据"""
    try:
        db = get_database()
        collection = db[db_config["collection_name"]]

        filters = build_filters(time_range, env, version, level)

        # 用户错误分布
        user_pipeline = [
            {"$match": filters},
            {"$group": {"_id": "$username", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}},
            {"$limit": 10},
        ]
        user_distribution = [
            {"user": doc["_id"], "count": doc["count"]}
            for doc in collection.aggregate(user_pipeline)
        ]

        # 环境错误分布
        env_pipeline = [
            {"$match": filters},
            {"$group": {"_id": "$env_name", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}},
        ]
        env_distribution = [
            {"env": doc["_id"], "count": doc["count"]}
            for doc in collection.aggregate(env_pipeline)
        ]

        # 错误等级分布
        level_pipeline = [
            {"$match": filters},
            {"$group": {"_id": "$level", "count": {"$sum": 1}}},
            {"$sort": {"_id": 1}},
        ]
        level_distribution = [
            {"level": doc["_id"], "count": doc["count"]}
            for doc in collection.aggregate(level_pipeline)
        ]

        # 错误类型分布
        type_pipeline = [
            {"$match": filters},
            {
                "$project": {
                    "error_type": {
                        "$regexFind": {"input": "$message", "regex": "<([^>]+)>"}
                    }
                }
            },
            {
                "$project": {
                    "error_type": {
                        "$ifNull": [
                            {"$arrayElemAt": ["$error_type.captures", 0]},
                            "未知错误",
                        ]
                    }
                }
            },
            {"$group": {"_id": "$error_type", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}},
            {"$limit": 10},
        ]
        type_distribution = [
            {"type": doc["_id"], "count": doc["count"]}
            for doc in collection.aggregate(type_pipeline)
        ]

        # 版本分布
        version_pipeline = [
            {"$match": filters},
            {
                "$project": {
                    "version_clean": {
                        "$trim": {
                            "input": {
                                "$arrayElemAt": [{"$split": ["$version", "|"]}, 1]
                            }
                        }
                    }
                }
            },
            {"$group": {"_id": "$version_clean", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}},
        ]
        version_distribution = [
            {"version": doc["_id"], "count": doc["count"]}
            for doc in collection.aggregate(version_pipeline)
        ]

        return {
            "by_user": user_distribution,
            "by_env": env_distribution,
            "by_level": level_distribution,
            "by_type": type_distribution,
            "by_version": version_distribution,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取错误分布数据失败: {str(e)}")


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=5000)
