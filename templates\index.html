<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monster框架错误分析看板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        secondary: '#36CFC9',
                        warning: '#FF7D00',
                        danger: '#F53F3F',
                        dark: '#1D2129',
                        'dark-2': '#4E5969',
                        'light-1': '#F2F3F5',
                        'light-2': '#E5E6EB'
                    }
                }
            }
        }
    </script>
    
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .card-shadow {
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            }
            .transition-all-300 {
                transition: all 300ms ease-in-out;
            }
            .loading-overlay {
                background-color: rgba(255, 255, 255, 0.7);
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-dark">
    <!-- 顶部导航 -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <i class="fa fa-bug text-danger text-2xl"></i>
                <h1 class="text-xl font-bold text-dark">Monster框架错误分析看板</h1>
            </div>
            <div class="flex items-center space-x-6">
                <div class="text-sm text-dark-2">
                    <span id="current-date"></span>
                </div>
                <div class="relative">
                    <button class="flex items-center space-x-1 text-sm text-dark-2 hover:text-primary transition-all-300">
                        <i class="fa fa-user-circle-o"></i>
                        <span>monitor</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 筛选区域 -->
    <div class="bg-white border-b border-light-2 py-3">
        <div class="container mx-auto px-4">
            <div class="flex flex-wrap gap-4 items-center">
                <div class="flex items-center space-x-2">
                    <label class="text-sm text-dark-2">时间范围:</label>
                    <select id="time-range" class="border border-light-2 rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="7d">近7天</option>
                        <option value="30d">近30天</option>
                        <option value="90d">近90天</option>
                        <option value="all">全部时间</option>
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm text-dark-2">环境:</label>
                    <select id="env-filter" class="border border-light-2 rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="all">全部环境</option>
                        <!-- 动态填充环境选项 -->
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm text-dark-2">版本:</label>
                    <select id="version-filter" class="border border-light-2 rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="all">全部版本</option>
                        <!-- 动态填充版本选项 -->
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm text-dark-2">错误等级:</label>
                    <select id="level-filter" class="border border-light-2 rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="all">全部等级</option>
                        <!-- 动态填充等级选项 -->
                    </select>
                </div>
                
                <div class="flex items-center space-x-2 ml-auto">
                    <button id="refresh-data" class="flex items-center space-x-1 text-sm text-primary hover:text-primary/80 transition-all-300">
                        <i class="fa fa-refresh"></i>
                        <span>刷新数据</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="container mx-auto px-4 py-6">
        <!-- 加载状态 -->
        <div id="loading-overlay" class="fixed inset-0 z-50 loading-overlay hidden items-center justify-center">
            <div class="bg-white p-6 rounded-lg shadow-lg flex items-center">
                <i class="fa fa-spinner fa-spin text-primary text-2xl mr-3"></i>
                <span>加载数据中...</span>
            </div>
        </div>
        
        <!-- 错误提示 -->
        <div id="error-alert" class="bg-danger/10 border border-danger/20 text-danger p-4 rounded-lg mb-6 hidden">
            <div class="flex items-center">
                <i class="fa fa-exclamation-circle mr-2"></i>
                <span id="error-message">加载数据失败，请重试</span>
            </div>
        </div>

        <!-- 关键指标卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg p-5 card-shadow hover:shadow-lg transition-all-300">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-dark-2 text-sm mb-1">总错误数量</p>
                        <h3 class="text-2xl font-bold text-dark" id="total-errors">--</h3>
                    </div>
                    <div class="w-10 h-10 rounded-full bg-danger/10 flex items-center justify-center">
                        <i class="fa fa-exclamation-triangle text-danger"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-danger flex items-center">
                        <i class="fa fa-arrow-up mr-1"></i><span id="total-errors-change">--</span>
                    </span>
                    <span class="text-dark-2 ml-2">较昨日</span>
                </div>
            </div>
            
            <div class="bg-white rounded-lg p-5 card-shadow hover:shadow-lg transition-all-300">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-dark-2 text-sm mb-1">独特错误类型</p>
                        <h3 class="text-2xl font-bold text-dark" id="unique-errors">--</h3>
                    </div>
                    <div class="w-10 h-10 rounded-full bg-warning/10 flex items-center justify-center">
                        <i class="fa fa-list-alt text-warning"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-warning flex items-center">
                        <i class="fa fa-minus mr-1"></i><span id="unique-errors-change">--</span>
                    </span>
                    <span class="text-dark-2 ml-2">较昨日</span>
                </div>
            </div>
            
            <div class="bg-white rounded-lg p-5 card-shadow hover:shadow-lg transition-all-300">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-dark-2 text-sm mb-1">处理率</p>
                        <h3 class="text-2xl font-bold text-dark" id="resolution-rate">--</h3>
                    </div>
                    <div class="w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center">
                        <i class="fa fa-check-circle text-secondary"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-secondary flex items-center">
                        <i class="fa fa-arrow-up mr-1"></i><span id="resolution-rate-change">--</span>
                    </span>
                    <span class="text-dark-2 ml-2">较昨日</span>
                </div>
            </div>
            
            <div class="bg-white rounded-lg p-5 card-shadow hover:shadow-lg transition-all-300">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-dark-2 text-sm mb-1">错误率</p>
                        <h3 class="text-2xl font-bold text-dark" id="error-rate">--</h3>
                    </div>
                    <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <i class="fa fa-percent text-primary"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-danger flex items-center">
                        <i class="fa fa-arrow-up mr-1"></i><span id="error-rate-change">--</span>
                    </span>
                    <span class="text-dark-2 ml-2">较昨日</span>
                </div>
            </div>
        </div>
        
        <!-- 时间趋势大图 -->
        <div class="grid grid-cols-1 gap-6 mb-6">
            <div class="bg-white rounded-lg p-5 card-shadow">
                <div class="flex flex-wrap justify-between items-center mb-5 gap-3">
                    <h3 class="font-semibold text-dark text-lg">错误类型与总数时间趋势</h3>
                    <div class="flex flex-wrap gap-2">
                        <button class="text-xs px-3 py-1 rounded-full bg-light-1 text-dark-2">小时</button>
                        <button class="text-xs px-3 py-1 rounded-full bg-primary/10 text-primary">天</button>
                        <button class="text-xs px-3 py-1 rounded-full bg-light-1 text-dark-2">周</button>
                        <button class="text-xs px-3 py-1 rounded-full bg-light-1 text-dark-2">月</button>
                    </div>
                </div>
                <div class="h-96">
                    <canvas id="error-type-time-trend"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 四个图表2x2排列 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- 用户错误分布 -->
            <div class="bg-white rounded-lg p-5 card-shadow">
                <div class="flex justify-between items-center mb-5">
                    <h3 class="font-semibold text-dark">用户错误分布</h3>
                    <button class="text-primary text-sm hover:underline">
                        <i class="fa fa-ellipsis-v"></i>
                    </button>
                </div>
                <div class="h-64">
                    <canvas id="user-error-chart"></canvas>
                </div>
            </div>
            
            <!-- 环境错误分布 -->
            <div class="bg-white rounded-lg p-5 card-shadow">
                <div class="flex justify-between items-center mb-5">
                    <h3 class="font-semibold text-dark">环境错误分布</h3>
                    <button class="text-primary text-sm hover:underline">
                        <i class="fa fa-filter"></i>
                    </button>
                </div>
                <div class="h-64">
                    <canvas id="environment-chart"></canvas>
                </div>
            </div>
            
            <!-- 错误等级分布 -->
            <div class="bg-white rounded-lg p-5 card-shadow">
                <div class="flex justify-between items-center mb-5">
                    <h3 class="font-semibold text-dark">错误等级分布</h3>
                    <button class="text-primary text-sm hover:underline">
                        <i class="fa fa-filter"></i>
                    </button>
                </div>
                <div class="h-64">
                    <canvas id="level-chart"></canvas>
                </div>
            </div>
            
            <!-- 错误类型分布 -->
            <div class="bg-white rounded-lg p-5 card-shadow">
                <div class="flex justify-between items-center mb-5">
                    <h3 class="font-semibold text-dark">错误类型分布</h3>
                    <button class="text-primary text-sm hover:underline">
                        <i class="fa fa-ellipsis-v"></i>
                    </button>
                </div>
                <div class="h-64">
                    <canvas id="error-type-chart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 版本与错误类型堆叠图 -->
        <div class="grid grid-cols-1 gap-6 mb-6">
            <div class="bg-white rounded-lg p-5 card-shadow">
                <div class="flex justify-between items-center mb-5">
                    <h3 class="font-semibold text-dark">版本-错误类型-等级 分布</h3>
                    <div class="flex space-x-2">
                        <button class="text-xs px-3 py-1 rounded-full bg-primary/10 text-primary">按类型</button>
                        <button class="text-xs px-3 py-1 rounded-full bg-light-1 text-dark-2">按等级</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="version-error-type-stack-chart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 错误详情列表 -->
        <div class="bg-white rounded-lg p-5 card-shadow mb-6">
            <div class="flex flex-wrap justify-between items-center mb-5 gap-3">
                <h3 class="font-semibold text-dark">错误详情列表</h3>
                <div class="relative">
                    <input type="text" id="error-search" placeholder="搜索错误..." class="pl-8 pr-3 py-1.5 border border-light-2 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary w-64">
                    <i class="fa fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-dark-2"></i>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-light-2">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-dark-2 uppercase tracking-wider">时间</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-dark-2 uppercase tracking-wider">芯片</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-dark-2 uppercase tracking-wider">等级</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-dark-2 uppercase tracking-wider">环境</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-dark-2 uppercase tracking-wider">版本</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-dark-2 uppercase tracking-wider">状态</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-dark-2 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-light-2" id="errors-table-body">
                        <!-- 表格内容将通过JavaScript动态填充 -->
                        <tr>
                            <td colspan="7" class="px-4 py-8 text-center text-dark-2">
                                <i class="fa fa-spinner fa-spin mr-2"></i>加载错误列表中...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="flex justify-between items-center mt-4 text-sm">
                <div class="text-dark-2" id="pagination-info">显示 0 到 0，共 0 条记录</div>
                <div class="flex space-x-1" id="pagination-controls">
                    <button class="w-8 h-8 flex items-center justify-center rounded border border-light-2 text-dark-2 hover:border-primary hover:text-primary disabled:opacity-50" disabled>
                        <i class="fa fa-chevron-left text-xs"></i>
                    </button>
                    <button class="w-8 h-8 flex items-center justify-center rounded border border-light-2 text-dark-2 hover:border-primary hover:text-primary" disabled>
                        1
                    </button>
                    <button class="w-8 h-8 flex items-center justify-center rounded border border-light-2 text-dark-2 hover:border-primary hover:text-primary disabled:opacity-50" disabled>
                        <i class="fa fa-chevron-right text-xs"></i>
                    </button>
                </div>
            </div>
        </div>
    </main>
    
    <!-- 错误详情模态框 -->
    <div id="error-detail-modal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center">
        <div class="bg-white rounded-lg w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
            <div class="p-5 border-b border-light-2 flex justify-between items-center">
                <h3 class="font-semibold text-dark text-lg">错误详情</h3>
                <button id="close-modal" class="text-dark-2 hover:text-dark transition-all-300">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            
            <div class="p-5 overflow-y-auto flex-grow" id="error-detail-content">
                <!-- 错误详情加载中 -->
                <div class="flex justify-center items-center h-64">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin text-primary text-2xl mb-2"></i>
                        <p class="text-dark-2">加载错误详情中...</p>
                    </div>
                </div>
            </div>
            
            <div class="p-5 border-t border-light-2 flex justify-end space-x-3">
                <button id="copy-error-details" class="px-4 py-2 border border-light-2 rounded-md text-dark-2 hover:bg-light-1 transition-all-300">
                    复制详情
                </button>
                <button id="mark-as-handled" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-all-300">
                    标记为已处理
                </button>
            </div>
        </div>
    </div>

    <script>
        // API基础地址 - 实际部署时修改为后端服务地址
        const API_BASE_URL = 'http://localhost:5000/api';
        let currentErrorId = null; // 当前查看的错误ID
        let currentPage = 1;
        const itemsPerPage = 20;

        // 辅助函数：提取错误类型
        function extractErrorType(message) {
            const match = message.match(/<.*?>/);
            return match ? match[0] : '未知错误';
        }
        
        // 辅助函数：提取版本号
        function extractVersion(versionString) {
            return versionString.split('|')[1]?.trim() || versionString;
        }

        // API请求工具函数
        async function apiRequest(endpoint, method = 'GET', data = null) {
            showLoading();
            
            try {
                const url = `${API_BASE_URL}${endpoint}`;
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                
                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('API请求错误:', error);
                showError(`加载数据失败: ${error.message}`);
                throw error;
            } finally {
                hideLoading();
            }
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading-overlay').classList.remove('hidden');
            document.getElementById('loading-overlay').classList.add('flex');
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading-overlay').classList.remove('flex');
            document.getElementById('loading-overlay').classList.add('hidden');
        }

        // 显示错误提示
        function showError(message) {
            const errorAlert = document.getElementById('error-alert');
            document.getElementById('error-message').textContent = message;
            errorAlert.classList.remove('hidden');
            
            // 3秒后自动隐藏
            setTimeout(() => {
                errorAlert.classList.add('hidden');
            }, 3000);
        }

        // 获取筛选条件
        function getFilterParams() {
            return {
                time_range: document.getElementById('time-range').value,
                env: document.getElementById('env-filter').value,
                version: document.getElementById('version-filter').value,
                level: document.getElementById('level-filter').value,
                search: document.getElementById('error-search').value.trim()
            };
        }

        // 构建查询字符串
        function buildQueryString(params) {
            const queryParams = new URLSearchParams();
            Object.entries(params).forEach(([key, value]) => {
                if (value && value !== 'all') {
                    queryParams.append(key, value);
                }
            });
            return queryParams.toString() ? `?${queryParams.toString()}` : '';
        }

        // 初始化筛选选项
        async function initFilterOptions() {
            try {
                const data = await apiRequest('/filter/options');
                
                // 填充环境选项
                const envFilter = document.getElementById('env-filter');
                data.envs.forEach(env => {
                    const option = document.createElement('option');
                    option.value = env;
                    option.textContent = env;
                    envFilter.appendChild(option);
                });
                
                // 填充版本选项
                const versionFilter = document.getElementById('version-filter');
                data.versions.forEach(version => {
                    const option = document.createElement('option');
                    option.value = version;
                    option.textContent = version;
                    versionFilter.appendChild(option);
                });
                
                // 填充等级选项
                const levelFilter = document.getElementById('level-filter');
                data.levels.forEach(level => {
                    const option = document.createElement('option');
                    option.value = level;
                    option.textContent = `等级 ${level}`;
                    levelFilter.appendChild(option);
                });
            } catch (error) {
                console.error('初始化筛选选项失败:', error);
            }
        }

        // 加载错误统计数据
        async function loadErrorStats() {
            try {
                const params = getFilterParams();
                const queryString = buildQueryString(params);
                const data = await apiRequest(`/error/stats${queryString}`);
                
                // 更新统计指标
                document.getElementById('total-errors').textContent = data.total_errors;
                document.getElementById('unique-errors').textContent = data.unique_errors;
                document.getElementById('resolution-rate').textContent = `${data.resolution_rate}%`;
                document.getElementById('error-rate').textContent = `${data.error_rate}%`;
                
                // 更新变化率（这里假设API返回了这些数据）
                document.getElementById('total-errors-change').textContent = data.total_errors_change || '0%';
                document.getElementById('unique-errors-change').textContent = data.unique_errors_change || '0%';
                document.getElementById('resolution-rate-change').textContent = data.resolution_rate_change || '0%';
                document.getElementById('error-rate-change').textContent = data.error_rate_change || '0%';
                
                return data;
            } catch (error) {
                console.error('加载错误统计失败:', error);
                return null;
            }
        }

        // 加载错误列表
        async function loadErrorList(page = 1) {
            try {
                currentPage = page;
                const params = {
                    ...getFilterParams(),
                    page,
                    per_page: itemsPerPage
                };
                
                const queryString = buildQueryString(params);
                const data = await apiRequest(`/errors${queryString}`);
                
                // 填充错误表格
                const tableBody = document.getElementById('errors-table-body');
                tableBody.innerHTML = '';
                
                if (data.errors.length === 0) {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="7" class="px-4 py-8 text-center text-dark-2">
                                没有找到匹配的错误记录
                            </td>
                        </tr>
                    `;
                } else {
                    data.errors.forEach(error => {
                        const row = document.createElement('tr');
                        row.className = 'hover:bg-light-1/50 transition-all-300';
                        row.innerHTML = `
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-dark">${error.time}</td>
                            <td class="px-4 py-3 text-sm text-dark max-w-xs truncate">${error.sample}</td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm">
                                <span class="px-2 py-1 text-xs rounded-full bg-warning/10 text-warning">${error.level}</span>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-dark">${error.env_name}</td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-dark">${extractVersion(error.version)}</td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs rounded-full ${error.tackle ? 'bg-secondary/10 text-secondary' : 'bg-warning/10 text-warning'}">
                                    ${error.tackle ? '已处理' : '未处理'}
                                </span>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm">
                                <button class="text-primary hover:text-primary/80 view-error-details" data-id="${error._id}">
                                    查看详情
                                </button>
                            </td>
                        `;
                        
                        tableBody.appendChild(row);
                    });
                }
                
                // 更新分页信息
                document.getElementById('pagination-info').textContent = 
                    `显示 ${(page - 1) * itemsPerPage + 1} 到 ${Math.min(page * itemsPerPage, data.total)}，共 ${data.total} 条记录`;
                
                // 更新分页控件
                updatePaginationControls(data.total, page);
                
                return data;
            } catch (error) {
                console.error('加载错误列表失败:', error);
                return null;
            }
        }

        // 更新分页控件
        function updatePaginationControls(totalItems, currentPage) {
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const controlsContainer = document.getElementById('pagination-controls');
            
            controlsContainer.innerHTML = `
                <button class="w-8 h-8 flex items-center justify-center rounded border border-light-2 text-dark-2 hover:border-primary hover:text-primary disabled:opacity-50" 
                        id="prev-page" ${currentPage <= 1 ? 'disabled' : ''}>
                    <i class="fa fa-chevron-left text-xs"></i>
                </button>
                <button class="w-8 h-8 flex items-center justify-center rounded border ${currentPage === 1 ? 'border-primary bg-primary text-white' : 'border-light-2 text-dark-2 hover:border-primary hover:text-primary'}"
                        data-page="1">1</button>
                ${totalPages > 2 ? `<span class="w-8 h-8 flex items-center justify-center">...</span>` : ''}
                ${totalPages > 1 ? `
                <button class="w-8 h-8 flex items-center justify-center rounded border ${currentPage === totalPages ? 'border-primary bg-primary text-white' : 'border-light-2 text-dark-2 hover:border-primary hover:text-primary'}"
                        data-page="${totalPages}">${totalPages}</button>
                ` : ''}
                <button class="w-8 h-8 flex items-center justify-center rounded border border-light-2 text-dark-2 hover:border-primary hover:text-primary disabled:opacity-50" 
                        id="next-page" ${currentPage >= totalPages ? 'disabled' : ''}>
                    <i class="fa fa-chevron-right text-xs"></i>
                </button>
            `;
            
            // 绑定分页事件
            document.getElementById('prev-page').addEventListener('click', () => {
                if (currentPage > 1) {
                    loadErrorList(currentPage - 1);
                }
            });
            
            document.getElementById('next-page').addEventListener('click', () => {
                if (currentPage < totalPages) {
                    loadErrorList(currentPage + 1);
                }
            });
            
            // 页码点击事件
            document.querySelectorAll('#pagination-controls button[data-page]').forEach(btn => {
                btn.addEventListener('click', () => {
                    const page = parseInt(btn.getAttribute('data-page'));
                    loadErrorList(page);
                });
            });
        }

        // 加载错误分布数据并初始化图表
        async function loadDistributionsAndInitCharts() {
            try {
                const params = getFilterParams();
                const queryString = buildQueryString(params);
                const distributions = await apiRequest(`/error/distributions${queryString}`);
                
                // 同时获取错误列表数据用于图表
                const errorStats = await loadErrorStats();
                const errorListData = await apiRequest(`/errors${buildQueryString({...params, per_page: 1000})}`);
                
                // 初始化所有图表
                initCharts(distributions, errorStats, errorListData.errors);
            } catch (error) {
                console.error('加载分布数据失败:', error);
            }
        }

        // 初始化图表
        function initCharts(distributions, errorStats, errorList) {
            // 销毁已存在的图表
            destroyExistingCharts();
            
            // 1. 错误类型与总数时间趋势图
            if (errorStats && errorStats.trends && errorStats.trends.daily_counts) {
                const typeTimeCtx = document.getElementById('error-type-time-trend').getContext('2d');
                
                // 处理时间趋势数据
                const timeGroups = {};
                errorList.forEach(e => {
                    const date = e.time.split(' ')[0];
                    const errorType = extractErrorType(e.message);
                    
                    if (!timeGroups[date]) {
                        timeGroups[date] = { total: 0 };
                    }
                    timeGroups[date].total++;
                    
                    if (!timeGroups[date][errorType]) {
                        timeGroups[date][errorType] = 0;
                    }
                    timeGroups[date][errorType]++;
                });
                
                const allErrorTypes = [...new Set(errorList.map(e => extractErrorType(e.message)))];
                const sortedDateLabels = Object.keys(timeGroups).sort();
                
                const datasets = allErrorTypes.map((type, index) => {
                    const colors = [
                        '#165DFF', '#36CFC9', '#FF7D00', '#F53F3F', '#722ED1',
                        '#FF5252', '#FFAB40', '#00B42A', '#1890FF', '#9254DE'
                    ];
                    
                    return {
                        label: type,
                        data: sortedDateLabels.map(date => timeGroups[date][type] || 0),
                        borderColor: colors[index % colors.length],
                        backgroundColor: `${colors[index % colors.length]}20`,
                        tension: 0.3,
                        fill: false,
                        borderWidth: 2,
                        yAxisID: 'y'
                    };
                });
                
                datasets.push({
                    label: '错误总数',
                    data: sortedDateLabels.map(date => timeGroups[date].total || 0),
                    borderColor: '#000000',
                    backgroundColor: 'rgba(0, 0, 0, 0.1)',
                    tension: 0.4,
                    fill: true,
                    borderWidth: 3,
                    pointRadius: 4,
                    yAxisID: 'y1',
                    order: -1
                });
                
                new Chart(typeTimeCtx, {
                    type: 'line',
                    data: {
                        labels: sortedDateLabels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            mode: 'index',
                            intersect: false,
                        },
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    boxWidth: 12,
                                    usePointStyle: true,
                                    pointStyle: 'circle'
                                }
                            }
                        },
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                title: { display: true, text: '错误类型数量' },
                                ticks: { precision: 0 }
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                title: { display: true, text: '错误总数' },
                                grid: { drawOnChartArea: false },
                                ticks: { precision: 0 }
                            }
                        }
                    }
                });
            }
            
            // 2. 用户错误分布图
            if (distributions && distributions.by_user) {
                const userCtx = document.getElementById('user-error-chart').getContext('2d');
                
                new Chart(userCtx, {
                    type: 'pie',
                    data: {
                        labels: distributions.by_user.map(item => item.user),
                        datasets: [{
                            data: distributions.by_user.map(item => item.count),
                            backgroundColor: ['#165DFF', '#36CFC9', '#FF7D00', '#F53F3F', '#722ED1'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: { boxWidth: 12, padding: 15 }
                            }
                        },
                        cutout: '60%'
                    }
                });
            }
            
            // 3. 环境错误分布图
            if (distributions && distributions.by_env) {
                const envCtx = document.getElementById('environment-chart').getContext('2d');
                
                new Chart(envCtx, {
                    type: 'bar',
                    data: {
                        labels: distributions.by_env.map(item => item.env),
                        datasets: [{
                            label: '错误数量',
                            data: distributions.by_env.map(item => item.count),
                            backgroundColor: '#165DFF',
                            borderRadius: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false } },
                        scales: {
                            y: { beginAtZero: true, ticks: { precision: 0 } }
                        }
                    }
                });
            }
            
            // 4. 错误等级分布图
            if (distributions && distributions.by_level) {
                const levelCtx = document.getElementById('level-chart').getContext('2d');
                
                new Chart(levelCtx, {
                    type: 'bar',
                    data: {
                        labels: distributions.by_level.map(item => `等级 ${item.level}`),
                        datasets: [{
                            label: '错误数量',
                            data: distributions.by_level.map(item => item.count),
                            backgroundColor: '#FF7D00',
                            borderRadius: 4
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false } },
                        scales: {
                            x: { beginAtZero: true, ticks: { precision: 0 } }
                        }
                    }
                });
            }
            
            // 5. 错误类型分布图
            if (distributions && distributions.by_type) {
                const errorTypeCtx = document.getElementById('error-type-chart').getContext('2d');
                
                new Chart(errorTypeCtx, {
                    type: 'doughnut',
                    data: {
                        labels: distributions.by_type.map(item => item.type),
                        datasets: [{
                            data: distributions.by_type.map(item => item.count),
                            backgroundColor: ['#F53F3F', '#FF7D00', '#FFCC00', '#36CFC9', '#165DFF', '#722ED1'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { position: 'bottom', labels: { boxWidth: 12, padding: 15 } },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.raw || 0;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = Math.round((value / total) * 100);
                                        return `${label}: ${value} (${percentage}%)`;
                                    }
                                }
                            }
                        },
                        cutout: '60%'
                    }
                });
            }
            
            // 6. 版本-错误类型堆叠柱状图
            if (distributions && distributions.by_version && distributions.by_type) {
                const versionStackCtx = document.getElementById('version-error-type-stack-chart').getContext('2d');
                
                // 处理版本-错误类型数据
                const versionTypeGroups = {};
                errorList.forEach(e => {
                    const version = extractVersion(e.version);
                    const errorType = extractErrorType(e.message);
                    
                    if (!versionTypeGroups[version]) versionTypeGroups[version] = {};
                    if (!versionTypeGroups[version][errorType]) versionTypeGroups[version][errorType] = 0;
                    versionTypeGroups[version][errorType]++;
                });
                
                const versions = Object.keys(versionTypeGroups);
                const errorTypes = [...new Set(errorList.map(e => extractErrorType(e.message)))];
                
                const stackDatasets = errorTypes.map((type, index) => {
                    const colors = [
                        '#165DFF', '#36CFC9', '#FF7D00', '#F53F3F', '#722ED1',
                        '#FF5252', '#FFAB40', '#00B42A', '#1890FF', '#9254DE'
                    ];
                    
                    return {
                        label: type,
                        data: versions.map(version => versionTypeGroups[version][type] || 0),
                        backgroundColor: colors[index % colors.length],
                        borderColor: colors[index % colors.length],
                        borderWidth: 1
                    };
                });
                
                new Chart(versionStackCtx, {
                    type: 'bar',
                    data: {
                        labels: versions,
                        datasets: stackDatasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: { boxWidth: 12, usePointStyle: true, pointStyle: 'circle' }
                            }
                        },
                        scales: {
                            x: { stacked: true, title: { display: true, text: 'Monster框架版本' } },
                            y: { 
                                stacked: true, 
                                beginAtZero: true, 
                                title: { display: true, text: '错误数量' },
                                ticks: { precision: 0 }
                            }
                        }
                    }
                });
            }
        }

        // 销毁已存在的图表
        function destroyExistingCharts() {
            const chartIds = [
                'error-type-time-trend',
                'user-error-chart',
                'environment-chart',
                'level-chart',
                'error-type-chart',
                'version-error-type-stack-chart'
            ];
            
            chartIds.forEach(id => {
                const chartInstance = Chart.getChart(id);
                if (chartInstance) {
                    chartInstance.destroy();
                }
            });
        }

        // 显示错误详情
        async function showErrorDetails(errorId) {
            if (!errorId) return;
            
            currentErrorId = errorId;
            const modal = document.getElementById('error-detail-modal');
            const content = document.getElementById('error-detail-content');
            
            // 显示加载状态
            content.innerHTML = `
                <div class="flex justify-center items-center h-64">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin text-primary text-2xl mb-2"></i>
                        <p class="text-dark-2">加载错误详情中...</p>
                    </div>
                </div>
            `;
            
            // 显示模态框
            modal.classList.remove('hidden');
            modal.classList.add('flex');
            
            try {
                // 从API获取错误详情
                const error = await apiRequest(`/errors/${errorId}`);
                
                const formattedMessage = error.message
                    .replace(/\n/g, '<br>')
                    .replace(/--------------------------------------------------/g, '<hr class="my-3 border-light-2">');
                
                const experimentMatch = error.message.match(/q1-.*?-Detune=\d+/);
                const experimentName = experimentMatch ? experimentMatch[0] : '未知实验';
                
                content.innerHTML = `
                    <div class="mb-4">
                        <h4 class="text-dark font-medium mb-2">实验信息</h4>
                        <p class="text-sm text-dark-2">${experimentName}</p>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <h4 class="text-dark font-medium mb-2">基本信息</h4>
                            <ul class="text-sm space-y-2">
                                <li><span class="text-dark-2">时间:</span> ${error.time}</li>
                                <li><span class="text-dark-2">芯片:</span> ${error.sample}</li>
                                <li><span class="text-dark-2">环境:</span> ${error.env_name}</li>
                                <li><span class="text-dark-2">版本:</span> ${error.version}</li>
                                <li><span class="text-dark-2">等级:</span> ${error.level}</li>
                                <li><span class="text-dark-2">状态:</span> <span class="${error.tackle ? 'text-secondary' : 'text-warning'}">${error.tackle ? '已处理' : '未处理'}</span></li>
                            </ul>
                        </div>
                        
                        <div>
                            <h4 class="text-dark font-medium mb-2">错误信息</h4>
                            <div class="text-sm text-danger">
                                ${formattedMessage.split('<hr')[1]?.split('<hr')[0] || formattedMessage}
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-dark font-medium mb-2">堆栈跟踪</h4>
                        <pre class="bg-light-1 p-3 rounded-md text-sm overflow-x-auto text-dark-2">${error.message.split('Traceback')[1] || '无堆栈信息'}</pre>
                    </div>
                `;
                
                // 更新"标记为已处理"按钮状态
                const markBtn = document.getElementById('mark-as-handled');
                markBtn.textContent = error.tackle ? '标记为未处理' : '标记为已处理';
                
            } catch (error) {
                console.error('加载错误详情失败:', error);
                content.innerHTML = `
                    <div class="flex justify-center items-center h-64">
                        <div class="text-center text-danger">
                            <i class="fa fa-exclamation-circle text-2xl mb-2"></i>
                            <p>加载错误详情失败，请重试</p>
                        </div>
                    </div>
                `;
            }
        }

        // 标记错误处理状态
        async function toggleErrorTackleStatus() {
            if (!currentErrorId) return;
            
            try {
                // 获取当前错误信息
                const error = await apiRequest(`/errors/${currentErrorId}`);
                
                // 调用API更新状态
                const result = await apiRequest(
                    `/errors/${currentErrorId}/tackle`,
                    'PUT',
                    { tackle: !error.tackle }
                );
                
                if (result.success) {
                    // 关闭模态框
                    document.getElementById('error-detail-modal').classList.remove('flex');
                    document.getElementById('error-detail-modal').classList.add('hidden');
                    
                    // 重新加载数据
                    refreshAllData();
                    
                    // 显示成功提示
                    showError(`错误已${result.tackle ? '标记为已处理' : '标记为未处理'}`);
                }
            } catch (error) {
                console.error('更新错误状态失败:', error);
                showError('更新错误状态失败，请重试');
            }
        }

        // 复制错误详情
        function copyErrorDetails() {
            const content = document.getElementById('error-detail-content').innerText;
            
            navigator.clipboard.writeText(content).then(() => {
                showError('错误详情已复制到剪贴板');
            }).catch(err => {
                console.error('复制失败:', err);
                showError('复制失败，请手动复制');
            });
        }

        // 刷新所有数据
        function refreshAllData() {
            const refreshBtn = document.getElementById('refresh-data');
            refreshBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i><span>刷新中...</span>';
            
            // 并行加载所有数据
            Promise.all([
                loadErrorStats(),
                loadErrorList(currentPage),
                loadDistributionsAndInitCharts()
            ]).then(() => {
                refreshBtn.innerHTML = '<i class="fa fa-refresh"></i><span>刷新数据</span>';
            }).catch(() => {
                refreshBtn.innerHTML = '<i class="fa fa-refresh"></i><span>刷新数据</span>';
            });
        }

        // 绑定事件
        function bindEvents() {
            // 查看错误详情
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('view-error-details') || e.target.parentElement.classList.contains('view-error-details')) {
                    const btn = e.target.classList.contains('view-error-details') ? e.target : e.target.parentElement;
                    const errorId = btn.getAttribute('data-id');
                    showErrorDetails(errorId);
                }
            });
            
            // 关闭模态框
            document.getElementById('close-modal').addEventListener('click', function() {
                document.getElementById('error-detail-modal').classList.remove('flex');
                document.getElementById('error-detail-modal').classList.add('hidden');
                currentErrorId = null;
            });
            
            // 点击模态框外部关闭
            document.getElementById('error-detail-modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.remove('flex');
                    this.classList.add('hidden');
                    currentErrorId = null;
                }
            });
            
            // 刷新数据
            document.getElementById('refresh-data').addEventListener('click', refreshAllData);
            
            // 筛选器变化时重新加载数据
            const filters = ['time-range', 'env-filter', 'version-filter', 'level-filter'];
            filters.forEach(filterId => {
                document.getElementById(filterId).addEventListener('change', () => {
                    // 重置到第一页
                    loadErrorList(1);
                    loadDistributionsAndInitCharts();
                    loadErrorStats();
                });
            });
            
            // 搜索错误
            document.getElementById('error-search').addEventListener('keyup', function(e) {
                // 按回车搜索或输入停止后搜索
                if (e.key === 'Enter' || this.value.length % 3 === 0) {
                    loadErrorList(1);
                    loadDistributionsAndInitCharts();
                    loadErrorStats();
                }
            });
            
            // 标记为已处理/未处理
            document.getElementById('mark-as-handled').addEventListener('click', toggleErrorTackleStatus);
            
            // 复制错误详情
            document.getElementById('copy-error-details').addEventListener('click', copyErrorDetails);
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 设置当前日期
            const now = new Date();
            document.getElementById('current-date').textContent = now.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });
            
            // 初始化筛选选项
            initFilterOptions();
            
            // 加载所有数据
            refreshAllData();
            
            // 绑定事件
            bindEvents();
        });
    </script>
</body>
</html>
