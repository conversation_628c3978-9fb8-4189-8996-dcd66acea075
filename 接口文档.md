# Monster框架错误分析看板 - 后端接口文档

基于前端需求和数据库示例，设计以下Flask后端接口：

## 1. 错误统计概览接口

**接口地址**: `/api/error/stats`  
**请求方法**: GET  
**请求参数**:
- `time_range`: 时间范围 (7d, 30d, 90d, all)，默认7d
- `env`: 环境名称，默认all
- `version`: 版本号，默认all
- `level`: 错误等级，默认all

**响应示例**:
```json
{
  "total_errors": 120,
  "unique_errors": 8,
  "resolution_rate": 75,
  "error_rate": 3.2,
  "trends": {
    "daily_counts": [
      {"date": "2025-05-15", "count": 15},
      {"date": "2025-05-16", "count": 22}
    ]
  }
}
```

## 2. 错误列表接口

**接口地址**: `/api/errors`  
**请求方法**: GET  
**请求参数**:
- `time_range`: 时间范围 (7d, 30d, 90d, all)，默认7d
- `env`: 环境名称，默认all
- `version`: 版本号，默认all
- `level`: 错误等级，默认all
- `page`: 页码，默认1
- `per_page`: 每页数量，默认20
- `search`: 搜索关键词

**响应示例**:
```json
{
  "total": 120,
  "page": 1,
  "per_page": 20,
  "errors": [
    {
      "_id": "682d6b17a77f3478825739e1",
      "username": "monitor_y4",
      "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）",
      "env_name": "Y4",
      "level": 15,
      "time": "2025-05-21 13:59:37.019113",
      "message": "Task Timeout...",
      "version": "monster | ******** | B",
      "tackle": true,
      "create_time": "2025-05-21T13:56:39.153Z"
    },
    // 更多错误...
  ]
}
```

## 3. 错误详情接口

**接口地址**: `/api/errors/{error_id}`  
**请求方法**: GET  
**路径参数**:
- `error_id`: 错误ID

**响应示例**:
```json
{
  "_id": "682d6b17a77f3478825739e1",
  "username": "monitor_y4",
  "sample": "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）",
  "env_name": "Y4",
  "level": 15,
  "time": "2025-05-21 13:59:37.019113",
  "message": "Task Timeout\n--------------------------------------------------\nTask[RID(a3af8e27-473b-43c2-a84a-d3b0339f2834) | TID(03ecc3f3-53c0-48fa-95eb-40a9c9bbf63e) | DataType(iq) | Loop(2)]",
  "version": "monster | ******** | B",
  "tackle": true,
  "create_time": "2025-05-21T13:56:39.153Z"
}
```

## 4. 错误分布统计接口

**接口地址**: `/api/error/distributions`  
**请求方法**: GET  
**请求参数**:
- `time_range`: 时间范围 (7d, 30d, 90d, all)，默认7d
- `env`: 环境名称，默认all
- `version`: 版本号，默认all
- `level`: 错误等级，默认all

**响应示例**:
```json
{
  "by_user": [
    {"user": "monitor_y4", "count": 85},
    {"user": "monitor_y5", "count": 35}
  ],
  "by_env": [
    {"env": "Y4", "count": 100},
    {"env": "Y5", "count": 20}
  ],
  "by_level": [
    {"level": 10, "count": 60},
    {"level": 15, "count": 40},
    {"level": 20, "count": 20}
  ],
  "by_type": [
    {"type": "<Acq Task State Error>", "count": 75},
    {"type": "Task Timeout", "count": 45}
  ],
  "by_version": [
    {"version": "********", "count": 90},
    {"version": "0.23.1.8", "count": 30}
  ]
}
```

## 5. 错误处理状态更新接口

**接口地址**: `/api/errors/{error_id}/tackle`  
**请求方法**: PUT  
**路径参数**:
- `error_id`: 错误ID

**请求体**:
```json
{
  "tackle": true
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "错误状态已更新",
  "error_id": "682d6b17a77f3478825739e1",
  "tackle": true
}
```

## 6. 筛选条件选项接口

**接口地址**: `/api/filter/options`  
**请求方法**: GET  

**响应示例**:
```json
{
  "envs": ["Y4", "Y5", "Y6"],
  "versions": ["********", "0.23.1.8", "0.23.1.7"],
  "levels": [10, 15, 20, 25]
}
```

## 接口调用说明

1. 所有接口返回数据格式均为JSON
2. 时间范围参数说明:
   - `7d`: 最近7天
   - `30d`: 最近30天
   - `90d`: 最近90天
   - `all`: 所有时间
3. 分页参数用于控制错误列表的分页显示
4. 筛选参数可以组合使用，实现多条件筛选
