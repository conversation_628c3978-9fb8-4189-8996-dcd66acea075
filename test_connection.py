#!/usr/bin/env python3
"""
数据库连接测试脚本
用于验证MongoDB连接配置是否正确
"""

import json
import sys
from datetime import datetime

def load_config():
    """加载数据库配置"""
    try:
        with open('db_config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 错误: 找不到db_config.json配置文件")
        sys.exit(1)
    except json.JSONDecodeError:
        print("❌ 错误: 配置文件格式不正确")
        sys.exit(1)

def test_connection():
    """测试数据库连接"""
    config = load_config()
    
    try:
        from pymongo import MongoClient
        from bson import ObjectId
    except ImportError:
        print("❌ 错误: 请先安装pymongo: pip install pymongo")
        sys.exit(1)
    
    print("🔍 正在测试数据库连接...")
    print(f"📍 主机: {config['host']}:{config['port']}")
    print(f"🗄️  数据库: {config['db_name']}")
    print(f"📋 集合: {config['collection_name']}")
    print(f"👤 用户: {config['username']}")
    
    try:
        # 创建连接
        client = MongoClient(
            host=config['host'],
            port=config['port'],
            username=config['username'],
            password=config['password'],
            authSource=config['authSource'],
            serverSelectionTimeoutMS=10000  # 10秒超时
        )
        
        # 测试连接
        print("\n⏳ 正在连接数据库...")
        client.admin.command('ping')
        print("✅ 数据库连接成功!")
        
        # 获取数据库和集合
        db = client[config['db_name']]
        collection = db[config['collection_name']]
        
        # 获取基本统计信息
        print("\n📊 数据库统计信息:")
        total_count = collection.count_documents({})
        print(f"   总文档数: {total_count:,}")
        
        if total_count > 0:
            # 获取最新的一条记录
            latest_doc = collection.find_one(sort=[("create_time", -1)])
            if latest_doc:
                print(f"   最新记录时间: {latest_doc.get('time', '未知')}")
                print(f"   最新记录环境: {latest_doc.get('env_name', '未知')}")
            
            # 统计不同环境的数量
            env_stats = list(collection.aggregate([
                {"$group": {"_id": "$env_name", "count": {"$sum": 1}}},
                {"$sort": {"count": -1}},
                {"$limit": 5}
            ]))
            
            if env_stats:
                print("\n🌍 环境分布 (前5名):")
                for env in env_stats:
                    print(f"   {env['_id']}: {env['count']:,} 条记录")
            
            # 统计错误等级分布
            level_stats = list(collection.aggregate([
                {"$group": {"_id": "$level", "count": {"$sum": 1}}},
                {"$sort": {"_id": 1}}
            ]))
            
            if level_stats:
                print("\n⚠️  错误等级分布:")
                for level in level_stats:
                    print(f"   等级 {level['_id']}: {level['count']:,} 条记录")
            
            # 统计处理状态
            handled_count = collection.count_documents({"tackle": True})
            unhandled_count = total_count - handled_count
            handled_rate = (handled_count / total_count * 100) if total_count > 0 else 0
            
            print(f"\n🔧 处理状态:")
            print(f"   已处理: {handled_count:,} 条 ({handled_rate:.1f}%)")
            print(f"   未处理: {unhandled_count:,} 条 ({100-handled_rate:.1f}%)")
        
        # 测试查询性能
        print("\n⚡ 查询性能测试:")
        start_time = datetime.now()
        sample_docs = list(collection.find().limit(10))
        end_time = datetime.now()
        query_time = (end_time - start_time).total_seconds() * 1000
        print(f"   查询10条记录耗时: {query_time:.2f}ms")
        
        # 显示示例数据结构
        if sample_docs:
            print("\n📄 示例数据结构:")
            sample = sample_docs[0]
            for key, value in sample.items():
                if key == '_id':
                    print(f"   {key}: {str(value)} (ObjectId)")
                elif isinstance(value, str) and len(value) > 50:
                    print(f"   {key}: {value[:50]}... (长度: {len(value)})")
                else:
                    print(f"   {key}: {value}")
        
        client.close()
        print("\n✅ 数据库连接测试完成!")
        print("🚀 可以启动API服务了!")
        
    except Exception as e:
        print(f"\n❌ 数据库连接失败: {str(e)}")
        print("\n🔧 请检查以下项目:")
        print("   1. 数据库服务是否运行")
        print("   2. 网络连接是否正常")
        print("   3. 用户名和密码是否正确")
        print("   4. 数据库名称和集合名称是否存在")
        print("   5. 防火墙设置是否允许连接")
        sys.exit(1)

if __name__ == "__main__":
    test_connection()
