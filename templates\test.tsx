import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell, Area, AreaChart } from 'recharts';
import { Calendar, AlertTriangle, User, Settings, Clock, TrendingUp, Filter, Download } from 'lucide-react';

const MonsterErrorDashboard = () => {
  // 模拟数据 - 基于你提供的实际数据结构
  const [errorData] = useState([
    {
      _id: "682d69fea77f3478825739dd",
      username: "monitor_y4",
      sample: "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）",
      env_name: "Y4",
      level: 10,
      time: "2025-05-21 13:54:56.823822",
      error_type: "AcquisitionTaskStateError",
      experiment: "q1-SweepDetuneRabiWidth-RabiScanWidthDetune",
      detune_value: 30,
      version: "monster | ******** | B",
      tackle: true,
      create_time: "2025-05-21T13:51:58.954Z"
    },
    {
      _id: "682d6a59a77f3478825739de",
      username: "monitor_y4",
      sample: "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）",
      env_name: "Y4",
      level: 10,
      time: "2025-05-21 13:56:27.860202",
      error_type: "AcquisitionTaskStateError",
      experiment: "q1-SweepDetuneRabiWidth-RabiScanWidthDetune",
      detune_value: 34,
      version: "monster | ******** | B",
      tackle: true,
      create_time: "2025-05-21T13:53:29.989Z"
    },
    // 添加更多模拟数据以展示分析功能
    {
      _id: "682d6ab5a77f3478825739df",
      username: "monitor_y4",
      sample: "250328-设计验证-102bit-V3.1 (Si+Nb120nm+Ta30nm)-Base-2#（Flip-17#-B1）",
      env_name: "Y4",
      level: 10,
      time: "2025-05-21 13:57:58.897376",
      error_type: "AcquisitionTaskStateError",
      experiment: "q1-SweepDetuneRabiWidth-RabiScanWidthDetune",
      detune_value: 26,
      version: "monster | ******** | B",
      tackle: true,
      create_time: "2025-05-21T13:55:01.027Z"
    },
    // 添加其他类型的错误数据
    {
      _id: "682d6b10a77f3478825739e1",
      username: "monitor_y2",
      sample: "250401-实验样品-64bit-V2.8",
      env_name: "Y2",
      level: 8,
      time: "2025-05-21 14:12:45.123456",
      error_type: "CalibrationError",
      experiment: "q2-FrequencyCalibration",
      detune_value: null,
      version: "monster | ******** | A",
      tackle: false,
      create_time: "2025-05-21T14:10:12.456Z"
    },
    {
      _id: "682d6b10a77f3478825739e2",
      username: "monitor_y3",
      sample: "250415-测试芯片-128bit-V3.0",
      env_name: "Y3",
      level: 6,
      time: "2025-05-21 15:30:22.789012",
      error_type: "TimeoutError",
      experiment: "q3-GateSequenceOptimization",
      detune_value: null,
      version: "monster | ******** | B",
      tackle: true,
      create_time: "2025-05-21T15:28:33.789Z"
    }
  ]);

  const [selectedDateRange, setSelectedDateRange] = useState('7d');
  const [selectedEnvironment, setSelectedEnvironment] = useState('all');
  const [selectedErrorType, setSelectedErrorType] = useState('all');

  // 数据分析计算
  const analytics = useMemo(() => {
    let filteredData = errorData;

    // 按环境过滤
    if (selectedEnvironment !== 'all') {
      filteredData = filteredData.filter(item => item.env_name === selectedEnvironment);
    }

    // 按错误类型过滤
    if (selectedErrorType !== 'all') {
      filteredData = filteredData.filter(item => item.error_type === selectedErrorType);
    }

    // 错误类型统计
    const errorTypes = filteredData.reduce((acc, item) => {
      acc[item.error_type] = (acc[item.error_type] || 0) + 1;
      return acc;
    }, {});

    // 环境错误分布
    const envDistribution = filteredData.reduce((acc, item) => {
      acc[item.env_name] = (acc[item.env_name] || 0) + 1;
      return acc;
    }, {});

    // 实验类型错误分布
    const experimentTypes = filteredData.reduce((acc, item) => {
      const expType = item.experiment.split('-')[1] || 'Unknown';
      acc[expType] = (acc[expType] || 0) + 1;
      return acc;
    }, {});

    // 时间趋势分析
    const timeDistribution = filteredData.reduce((acc, item) => {
      const hour = new Date(item.time).getHours();
      const hourRange = `${hour}:00-${hour + 1}:00`;
      acc[hourRange] = (acc[hourRange] || 0) + 1;
      return acc;
    }, {});

    // 版本错误分布
    const versionDistribution = filteredData.reduce((acc, item) => {
      acc[item.version] = (acc[item.version] || 0) + 1;
      return acc;
    }, {});

    // 处理状态统计
    const tackleStatus = filteredData.reduce((acc, item) => {
      const status = item.tackle ? '已处理' : '未处理';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    return {
      totalErrors: filteredData.length,
      errorTypes: Object.entries(errorTypes).map(([name, value]) => ({ name, value })),
      envDistribution: Object.entries(envDistribution).map(([name, value]) => ({ name, value })),
      experimentTypes: Object.entries(experimentTypes).map(([name, value]) => ({ name, value })),
      timeDistribution: Object.entries(timeDistribution).map(([name, value]) => ({ name, value })),
      versionDistribution: Object.entries(versionDistribution).map(([name, value]) => ({ name, value })),
      tackleStatus: Object.entries(tackleStatus).map(([name, value]) => ({ name, value }))
    };
  }, [errorData, selectedEnvironment, selectedErrorType]);

  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1', '#d084d0', '#ffb347'];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 头部 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-4 flex items-center">
            <AlertTriangle className="mr-3 text-red-500" />
            Monster框架错误分析看板
          </h1>
          
          {/* 过滤器 */}
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-600" />
              <select 
                value={selectedEnvironment} 
                onChange={(e) => setSelectedEnvironment(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm"
              >
                <option value="all">所有环境</option>
                <option value="Y4">Y4环境</option>
                <option value="Y2">Y2环境</option>
                <option value="Y3">Y3环境</option>
              </select>
            </div>
            
            <div className="flex items-center gap-2">
              <select 
                value={selectedErrorType} 
                onChange={(e) => setSelectedErrorType(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm"
              >
                <option value="all">所有错误类型</option>
                <option value="AcquisitionTaskStateError">数据采集错误</option>
                <option value="CalibrationError">校准错误</option>
                <option value="TimeoutError">超时错误</option>
              </select>
            </div>

            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-600" />
              <select 
                value={selectedDateRange} 
                onChange={(e) => setSelectedDateRange(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm"
              >
                <option value="1d">过去1天</option>
                <option value="7d">过去7天</option>
                <option value="30d">过去30天</option>
              </select>
            </div>
          </div>
        </div>

        {/* 关键指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总错误数</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.totalErrors}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">活跃环境</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.envDistribution.length}</p>
              </div>
              <Settings className="h-8 w-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">错误类型</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.errorTypes.length}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">处理率</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.totalErrors > 0 ? 
                    Math.round((analytics.tackleStatus.find(s => s.name === '已处理')?.value || 0) / analytics.totalErrors * 100) : 0}%
                </p>
              </div>
              <Clock className="h-8 w-8 text-purple-500" />
            </div>
          </div>
        </div>

        {/* 图表区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* 错误类型分布 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">错误类型分布</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={analytics.errorTypes}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {analytics.errorTypes.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>

          {/* 环境错误分布 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">环境错误分布</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analytics.envDistribution}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* 实验类型错误分析 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">实验类型错误分析</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analytics.experimentTypes} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={100} />
                <Tooltip />
                <Bar dataKey="value" fill="#ffc658" />
              </BarChart>
            </ResponsiveContainer>
          </div>

          {/* 时间分布分析 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">错误时间分布</h3>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={analytics.timeDistribution}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Area type="monotone" dataKey="value" stroke="#8884d8" fill="#8884d8" />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* 版本和处理状态分析 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 版本错误分布 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">版本错误分布</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analytics.versionDistribution}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#ff7300" />
              </BarChart>
            </ResponsiveContainer>
          </div>

          {/* 处理状态分析 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">错误处理状态</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={analytics.tackleStatus}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({name, value, percent}) => `${name}: ${value} (${(percent * 100).toFixed(0)}%)`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  <Cell fill="#4ade80" />
                  <Cell fill="#f87171" />
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* 详细建议 */}
        <div className="mt-6 bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">优化建议</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-2">高频错误类型</h4>
              <p className="text-sm text-red-700">
                AcquisitionTaskStateError是主要错误类型，建议重点优化数据采集模块的稳定性
              </p>
            </div>
            
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">环境负载均衡</h4>
              <p className="text-sm text-blue-700">
                某些环境错误集中，建议检查环境配置和负载分布
              </p>
            </div>
            
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">实验参数优化</h4>
              <p className="text-sm text-green-700">
                部分实验类型错误率较高，建议优化实验参数范围和执行策略
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MonsterErrorDashboard;