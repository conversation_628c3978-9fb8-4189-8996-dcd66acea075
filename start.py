#!/usr/bin/env python3
"""
Monster框架错误分析看板后端服务启动脚本
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✓ Python版本: {sys.version}")

def check_config_file():
    """检查配置文件"""
    config_file = Path("db_config.json")
    if not config_file.exists():
        print("错误: 找不到db_config.json配置文件")
        sys.exit(1)
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        required_keys = ['host', 'port', 'db_name', 'collection_name', 'username', 'password']
        for key in required_keys:
            if key not in config:
                print(f"错误: 配置文件缺少必需的键: {key}")
                sys.exit(1)
        
        print("✓ 配置文件检查通过")
        return config
    except json.JSONDecodeError:
        print("错误: 配置文件格式不正确")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 读取配置文件失败: {e}")
        sys.exit(1)

def install_dependencies():
    """安装依赖"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("错误: 找不到requirements.txt文件")
        sys.exit(1)
    
    print("正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ 依赖安装完成")
    except subprocess.CalledProcessError:
        print("错误: 依赖安装失败")
        sys.exit(1)

def test_database_connection(config):
    """测试数据库连接"""
    try:
        from pymongo import MongoClient
        
        client = MongoClient(
            host=config['host'],
            port=config['port'],
            username=config['username'],
            password=config['password'],
            authSource=config['authSource'],
            serverSelectionTimeoutMS=5000  # 5秒超时
        )
        
        # 测试连接
        client.admin.command('ping')
        
        # 测试数据库和集合
        db = client[config['db_name']]
        collection = db[config['collection_name']]
        count = collection.count_documents({})
        
        print(f"✓ 数据库连接成功")
        print(f"✓ 数据库: {config['db_name']}")
        print(f"✓ 集合: {config['collection_name']}")
        print(f"✓ 文档数量: {count}")
        
        client.close()
        return True
    except ImportError:
        print("错误: pymongo未安装，请先安装依赖")
        return False
    except Exception as e:
        print(f"错误: 数据库连接失败: {e}")
        return False

def start_server(host="0.0.0.0", port=5000, reload=True):
    """启动服务器"""
    try:
        import uvicorn
        print(f"\n🚀 启动Monster框架错误分析API服务...")
        print(f"📍 服务地址: http://{host}:{port}")
        print(f"📖 API文档: http://{host}:{port}/docs")
        print(f"📚 ReDoc文档: http://{host}:{port}/redoc")
        print("\n按 Ctrl+C 停止服务\n")
        
        uvicorn.run(
            "app:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except ImportError:
        print("错误: uvicorn未安装，请先安装依赖")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"错误: 启动服务失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    print("=" * 50)
    print("Monster框架错误分析看板后端服务")
    print("=" * 50)
    
    # 检查Python版本
    check_python_version()
    
    # 检查配置文件
    config = check_config_file()
    
    # 安装依赖
    install_dependencies()
    
    # 测试数据库连接
    if not test_database_connection(config):
        print("\n⚠️  数据库连接失败，但服务仍将启动")
        print("请检查数据库配置和网络连接")
    
    # 启动服务
    start_server()

if __name__ == "__main__":
    main()
