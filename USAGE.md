# Monster框架错误分析看板 - 使用指南

## 快速开始

### 1. 环境准备

确保您的系统已安装：
- Python 3.8 或更高版本
- pip 包管理器

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置数据库

确保 `db_config.json` 文件包含正确的MongoDB连接信息：

```json
{
  "host": "***********",
  "port": 27017,
  "db_name": "UserData",
  "collection_name": "ServerMessage",
  "username": "bylz",
  "password": "fjsaoJOIjiojj28hjj",
  "authSource": "admin"
}
```

### 4. 测试数据库连接

```bash
python test_connection.py
```

### 5. 启动服务

#### 方式一：使用启动脚本（推荐）
```bash
python start.py
```

#### 方式二：直接启动
```bash
python app.py
```

#### 方式三：使用uvicorn
```bash
uvicorn app:app --host 0.0.0.0 --port 5000 --reload
```

### 6. 访问服务

- **API服务**: http://localhost:5000
- **API文档**: http://localhost:5000/docs
- **ReDoc文档**: http://localhost:5000/redoc

## API接口说明

### 筛选选项
```http
GET /api/filter/options
```
获取所有可用的筛选选项（环境、版本、错误等级）

### 错误统计
```http
GET /api/error/stats?time_range=7d&env=all&version=all&level=all
```
获取错误统计数据，支持时间范围和条件筛选

### 错误列表
```http
GET /api/errors?page=1&per_page=20&time_range=7d&search=keyword
```
获取错误列表，支持分页、筛选和搜索

### 错误详情
```http
GET /api/errors/{error_id}
```
获取单个错误的详细信息

### 更新处理状态
```http
PUT /api/errors/{error_id}/tackle
Content-Type: application/json

{
  "tackle": true
}
```

### 错误分布统计
```http
GET /api/error/distributions?time_range=7d
```
获取各维度的错误分布统计数据

## 测试

### 运行API测试
```bash
python test_api.py
```

### 测试数据库连接
```bash
python test_connection.py
```

## 部署

### Docker部署
```bash
# 构建镜像
docker build -t monster-api .

# 运行容器
docker run -d --name monster-api -p 5000:5000 \
  -v $(pwd)/db_config.json:/app/db_config.json:ro \
  --restart unless-stopped monster-api
```

### Docker Compose部署
```bash
docker-compose up -d
```

### 生产环境部署
```bash
# 使用部署脚本
python deploy.py --method docker
# 或
python deploy.py --method systemd
```

## 性能优化建议

### 数据库索引
在MongoDB中创建以下索引以提高查询性能：

```javascript
// 时间索引
db.ServerMessage.createIndex({"create_time": -1})

// 环境索引
db.ServerMessage.createIndex({"env_name": 1})

// 等级索引
db.ServerMessage.createIndex({"level": 1})

// 处理状态索引
db.ServerMessage.createIndex({"tackle": 1})

// 复合索引
db.ServerMessage.createIndex({"env_name": 1, "level": 1, "create_time": -1})
```

### 查询优化
- 使用时间范围筛选避免全表扫描
- 合理设置分页大小（建议20-50条/页）
- 对于统计查询，考虑使用缓存

## 监控和日志

### 健康检查
```bash
curl http://localhost:5000/
```

### 查看日志
```bash
# 如果使用systemd
sudo journalctl -u monster-api -f

# 如果使用Docker
docker logs -f monster-api
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :5000
   # 或修改端口
   uvicorn app:app --port 5001
   ```

2. **数据库连接失败**
   - 检查网络连接
   - 验证数据库配置
   - 确认防火墙设置

3. **查询性能慢**
   - 检查数据库索引
   - 优化查询条件
   - 考虑数据分页

4. **内存使用过高**
   - 检查查询结果集大小
   - 调整分页参数
   - 优化聚合管道

### 调试模式
```bash
# 启用调试模式
uvicorn app:app --reload --log-level debug
```

## 配置说明

### 环境变量
可以通过环境变量覆盖默认配置：

```bash
export MONGO_HOST=localhost
export MONGO_PORT=27017
export API_PORT=5000
```

### 生产环境配置
- 设置适当的worker数量
- 配置日志级别
- 启用访问日志
- 设置超时参数

```bash
gunicorn app:app -w 4 -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:5000 \
  --timeout 60 \
  --access-logfile - \
  --error-logfile -
```

## 安全建议

1. **网络安全**
   - 使用防火墙限制访问
   - 配置HTTPS（生产环境）
   - 限制CORS域名

2. **数据库安全**
   - 使用强密码
   - 限制数据库用户权限
   - 启用数据库审计

3. **应用安全**
   - 定期更新依赖
   - 启用请求限制
   - 添加认证机制（如需要）

## 扩展开发

### 添加新接口
1. 在 `app.py` 中添加路由函数
2. 使用适当的HTTP方法和路径
3. 添加请求/响应模型
4. 更新API文档

### 自定义筛选条件
修改 `build_filters` 函数以支持新的筛选条件

### 添加缓存
可以集成Redis等缓存系统来提高性能

## 支持

如遇到问题，请：
1. 查看日志文件
2. 运行测试脚本
3. 检查数据库连接
4. 联系开发团队
