# Monster框架错误分析看板后端API

## 项目概述

这是一个基于FastAPI开发的后端API服务，为Monster框架错误分析看板提供数据支持。该服务连接MongoDB数据库，提供错误日志的查询、统计、分析等功能。

## 技术栈

- **Python 3.8+**
- **FastAPI** - 现代、快速的Web框架
- **MongoDB** - 文档数据库
- **PyMongo** - MongoDB Python驱动
- **Uvicorn** - ASGI服务器

## 选择FastAPI的理由

1. **高性能**: 基于Starlette和Pydantic，性能优秀
2. **自动文档**: 自动生成OpenAPI/Swagger文档
3. **类型提示**: 完整的类型提示支持，减少错误
4. **异步支持**: 原生支持异步处理，提高并发性能
5. **数据验证**: 自动请求/响应数据验证
6. **易于部署**: 支持多种部署方式

## 项目结构

```
LogBoard/
├── app.py              # 主应用文件
├── requirements.txt    # Python依赖
├── db_config.json     # 数据库配置
├── ServerMessage.json # 示例数据
├── templates/         # 前端模板
│   └── index.html    # 前端页面
└── README.md         # 项目说明
```

## API接口文档

### 基础接口

- `GET /` - 服务状态检查
- `GET /docs` - Swagger API文档
- `GET /redoc` - ReDoc API文档

### 筛选选项

- `GET /api/filter/options` - 获取筛选选项（环境、版本、等级）

### 错误统计

- `GET /api/error/stats` - 获取错误统计数据
  - 参数: time_range, env, version, level

### 错误列表

- `GET /api/errors` - 获取错误列表（支持分页和筛选）
  - 参数: page, per_page, time_range, env, version, level, search

### 错误详情

- `GET /api/errors/{error_id}` - 获取单个错误详情
- `PUT /api/errors/{error_id}/tackle` - 更新错误处理状态

### 错误分布

- `GET /api/error/distributions` - 获取错误分布数据
  - 返回用户、环境、等级、类型、版本的分布统计

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置数据库

确保 `db_config.json` 中的MongoDB连接信息正确：

```json
{
  "host": "***********",
  "port": 27017,
  "db_name": "UserData",
  "collection_name": "ServerMessage",
  "username": "bylz",
  "password": "fjsaoJOIjiojj28hjj",
  "authSource": "admin"
}
```

### 3. 启动服务

```bash
python app.py
```

或使用uvicorn直接启动：

```bash
uvicorn app:app --host 0.0.0.0 --port 5000 --reload
```

### 4. 访问服务

- API服务: http://localhost:5000
- API文档: http://localhost:5000/docs
- ReDoc文档: http://localhost:5000/redoc

## 核心功能特性

### 1. 条件查询优化

- 支持时间范围筛选（7天、30天、90天、全部）
- 支持环境、版本、错误等级筛选
- 支持关键词搜索
- 使用MongoDB索引优化查询性能

### 2. 数据聚合分析

- 用户错误分布统计
- 环境错误分布统计
- 错误等级分布统计
- 错误类型分布统计
- 版本错误分布统计

### 3. 分页和性能优化

- 支持分页查询，避免大数据量查询
- 使用MongoDB聚合管道优化复杂查询
- 合理的查询限制和排序

### 4. 错误处理

- 完善的异常处理机制
- 详细的错误信息返回
- 数据验证和类型检查

## 部署建议

### 生产环境部署

1. **使用Gunicorn + Uvicorn**:
```bash
pip install gunicorn
gunicorn app:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:5000
```

2. **使用Docker**:
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "5000"]
```

3. **使用Nginx反向代理**:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 性能优化建议

1. **数据库索引**:
```javascript
// MongoDB中创建索引
db.ServerMessage.createIndex({"create_time": -1})
db.ServerMessage.createIndex({"env_name": 1})
db.ServerMessage.createIndex({"level": 1})
db.ServerMessage.createIndex({"tackle": 1})
```

2. **连接池配置**:
在生产环境中配置MongoDB连接池参数

3. **缓存策略**:
对于统计数据可以考虑使用Redis缓存

## 安全注意事项

1. **CORS配置**: 生产环境中应限制允许的域名
2. **数据库安全**: 使用强密码和适当的权限配置
3. **输入验证**: 已实现基本的输入验证，可根据需要加强
4. **日志记录**: 建议添加详细的日志记录

## 故障排除

### 常见问题

1. **数据库连接失败**:
   - 检查网络连接
   - 验证数据库配置信息
   - 确认数据库服务运行状态

2. **查询性能慢**:
   - 检查数据库索引
   - 优化查询条件
   - 考虑数据分页

3. **内存使用过高**:
   - 检查查询结果集大小
   - 优化聚合管道
   - 调整分页参数

## 联系信息

如有问题或建议，请联系开发团队。
